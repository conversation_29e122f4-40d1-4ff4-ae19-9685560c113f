<?php
/**
 * 测试Date类型SQL拼接修复
 * 验证修复后的日期查询逻辑是否正确
 */

// 模拟测试数据
$testCases = [
    // 单日期查询测试
    [
        'type' => 'single_date',
        'search' => [
            'field_name' => 'estimate_iteration_cycle',
            'type' => 'date',
            'value' => ['2024-11-15'],
            'operate_type' => ''
        ],
        'expected_start' => '2024-11-15 00:00:00',
        'expected_end' => '2024-11-15 23:59:59'
    ],
    
    // 日期范围查询测试
    [
        'type' => 'date_range',
        'search' => [
            'field_name' => 'estimate_iteration_cycle',
            'type' => 'date',
            'value' => ['2024-11-15', '2024-11-20'],
            'operate_type' => ''
        ],
        'expected_start' => '2024-11-15 00:00:00',
        'expected_end' => '2024-11-20 23:59:59'
    ]
];

echo "=== Date类型SQL拼接修复测试 ===\n\n";

foreach ($testCases as $index => $testCase) {
    echo "测试案例 " . ($index + 1) . ": " . $testCase['type'] . "\n";
    echo "输入: " . json_encode($testCase['search']['value']) . "\n";
    
    $search = $testCase['search'];
    
    // 模拟修复后的逻辑
    if (!isset($search['value'][1])) {
        // 单日期查询
        $start_time = date('Y-m-d 00:00:00', strtotime($search['value'][0]));
        $end_time = date('Y-m-d 23:59:59', strtotime($search['value'][0]));
        
        $query = "((JSON_EXTRACT(extends, '$.{$search['field_name']}[0]') >= ? AND JSON_EXTRACT(extends, '$.{$search['field_name']}[0]') <= ?)";
        $query .= " OR (JSON_EXTRACT(extends, '$.{$search['field_name']}[1]') >= ? AND JSON_EXTRACT(extends, '$.{$search['field_name']}[1]') <= ?)";
        $query .= " OR (JSON_EXTRACT(extends, '$.{$search['field_name']}') >= ? AND JSON_EXTRACT(extends, '$.{$search['field_name']}') <= ?))";
        $params = [$start_time, $end_time, $start_time, $end_time, $start_time, $end_time];
        
        echo "查询类型: 单日期查询\n";
    } else {
        // 日期范围查询
        $start_time = date('Y-m-d 00:00:00', strtotime($search['value'][0]));
        $end_time = date('Y-m-d 23:59:59', strtotime($search['value'][1]));
        
        $query = "((JSON_EXTRACT(extends, '$.{$search['field_name']}[0]') <= ? AND JSON_EXTRACT(extends, '$.{$search['field_name']}[1]') >= ?)";
        $query .= " OR (JSON_EXTRACT(extends, '$.{$search['field_name']}') >= ? AND JSON_EXTRACT(extends, '$.{$search['field_name']}') <= ?))";
        $params = [$end_time, $start_time, $start_time, $end_time];
        
        echo "查询类型: 日期范围查询\n";
    }
    
    echo "生成的开始时间: $start_time\n";
    echo "生成的结束时间: $end_time\n";
    echo "期望的开始时间: " . $testCase['expected_start'] . "\n";
    echo "期望的结束时间: " . $testCase['expected_end'] . "\n";
    
    // 验证结果
    $start_correct = ($start_time === $testCase['expected_start']);
    $end_correct = ($end_time === $testCase['expected_end']);
    
    echo "开始时间正确: " . ($start_correct ? "✓" : "✗") . "\n";
    echo "结束时间正确: " . ($end_correct ? "✓" : "✗") . "\n";
    echo "测试结果: " . (($start_correct && $end_correct) ? "通过" : "失败") . "\n";
    
    echo "生成的SQL查询:\n";
    echo $query . "\n";
    echo "参数: " . json_encode($params) . "\n";
    echo str_repeat("-", 50) . "\n\n";
}

echo "=== 修复说明 ===\n";
echo "1. 修复了时间格式不一致的问题，统一使用 Y-m-d H:i:s 格式\n";
echo "2. 修复了单日期查询的逻辑错误，现在正确查询当天数据\n";
echo "3. 优化了日期范围查询，使用正确的交集判断逻辑\n";
echo "4. 改进了边界条件处理，确保包含边界值\n";
echo "5. 同时支持JSON数组格式和单个字段格式的查询\n";
